import { render, screen } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { AppSidebar } from '../App-sidebar'
import { SidebarProvider } from '../ui/sidebar'

// Mock the useIsMobile hook
jest.mock('../../hooks/use-mobile', () => ({
  useIsMobile: () => false
}))

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <SidebarProvider>
        {component}
      </SidebarProvider>
    </BrowserRouter>
  )
}

describe('AppSidebar', () => {
  test('renders sidebar with navigation items', () => {
    renderWithRouter(<AppSidebar />)
    
    // Check if the sidebar title is rendered
    expect(screen.getByText('Kirana Shop')).toBeInTheDocument()
    
    // Check if navigation items are rendered
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Inventory')).toBeInTheDocument()
    expect(screen.getByText('Sales')).toBeInTheDocument()
    expect(screen.getByText('Customers')).toBeInTheDocument()
    expect(screen.getByText('Reports')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
  })

  test('renders footer with copyright', () => {
    renderWithRouter(<AppSidebar />)
    
    expect(screen.getByText('© 2024 Kirana Shop')).toBeInTheDocument()
  })
})
