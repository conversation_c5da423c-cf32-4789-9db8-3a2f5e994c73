// export default function Inventory() {
//     return (
//         <div className="flex flex-1 flex-col gap-4 p-4">
//             <div className="grid auto-rows-min gap-4 md:grid-cols-2">
//                 <div className="bg-muted/50 aspect-video rounded-xl" />
//                 <div className="bg-muted/50 aspect-video rounded-xl" />
//             </div>
//             <div className="bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min">
//                 <div className="p-6">
//                     <h1 className="text-2xl font-bold mb-4">Inventory Management</h1>
//                     <p className="text-muted-foreground">
//                         Manage your product inventory, track stock levels, and monitor product performance.
//                     </p>
//                 </div>
//             </div>
//         </div>
//     )
// }

import { Card, CardContent } from "@/components/ui/card";
import { Link } from "react-router-dom";

const inventoryFeatures = [
    { title: "All Products", to: "/inventory/products", desc: "View & manage products" },
    { title: "Add Product", to: "/inventory/add", desc: "Add new inventory items" },
    { title: "Stock In", to: "/inventory/stock-in", desc: "Record incoming stock" },
    { title: "Stock Out", to: "/inventory/stock-out", desc: "Adjust or reduce stock" },
    { title: "Low Stock Alerts", to: "/inventory/low-stock", desc: "Track low quantity items" },
    { title: "Expired Products", to: "/inventory/expired", desc: "View expired stock" },
    { title: "Categories", to: "/inventory/categories", desc: "Manage product categories" },
    { title: "Brands", to: "/inventory/brands", desc: "Manage brand filters" },
];

export default function InventoryDashboard() {
    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-2">Inventory Management</h1>
            <p className="text-muted-foreground mb-6">
                Manage your product inventory, track stock levels, and monitor product performance.
            </p>

            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <Card><CardContent className="p-4"><h2 className="font-semibold">Total Products</h2><p className="text-xl">152</p></CardContent></Card>
                <Card><CardContent className="p-4"><h2 className="font-semibold">Low Stock</h2><p className="text-xl">12</p></CardContent></Card>
                <Card><CardContent className="p-4"><h2 className="font-semibold">Expired</h2><p className="text-xl">3</p></CardContent></Card>
                <Card><CardContent className="p-4"><h2 className="font-semibold">Inactive</h2><p className="text-xl">8</p></CardContent></Card>
            </div>

            {/* Feature Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {inventoryFeatures.map((feature) => (
                    <Link to={feature.to} key={feature.to}>
                        <Card className="hover:shadow-md transition">
                            <CardContent className="p-4">
                                <h2 className="font-semibold mb-1">{feature.title}</h2>
                                <p className="text-muted-foreground text-sm">{feature.desc}</p>
                            </CardContent>
                        </Card>
                    </Link>
                ))}
            </div>
        </div>
    );
}

