import React, { useState } from 'react';

interface NavbarProps {
    currentPage?: string;
}

export const Navbar: React.FC<NavbarProps> = ({ currentPage = 'Dashboard' }) => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const navItems = ['Dashboard', 'Inventory', 'Sales', 'Customers', 'Reports'];

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    return (
        <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6 xl:px-8">
                <div className="flex justify-between items-center h-12 sm:h-14 lg:h-16">
                    {/* Logo */}
                    <div className="flex-shrink-0">
                        <h1 className="text-base sm:text-lg lg:text-xl xl:text-2xl font-semibold text-gray-900 truncate">
                            KiranaEase
                        </h1>
                    </div>

                    {/* Desktop Navigation Links - Hidden on mobile and small tablets */}
                    <div className="hidden lg:block flex-1 max-w-3xl mx-4 xl:mx-8">
                        <div className="flex items-center justify-center space-x-2 xl:space-x-6">
                            {navItems.map((item) => (
                                <a
                                    key={item}
                                    href="#"
                                    className={`px-2 xl:px-3 py-2 text-xs xl:text-sm font-medium transition-colors duration-200 whitespace-nowrap ${currentPage === item
                                        ? 'text-blue-600 border-b-2 border-blue-600'
                                        : 'text-gray-600 hover:text-gray-900 hover:border-b-2 hover:border-gray-300'
                                        }`}
                                >
                                    {item}
                                </a>
                            ))}
                        </div>
                    </div>

                    {/* Right side - Profile and Mobile menu button */}
                    <div className="flex items-center space-x-2 sm:space-x-3">
                        {/* Profile Avatar */}
                        <div className="flex-shrink-0">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-9 lg:h-9 bg-gray-400 rounded-full flex items-center justify-center overflow-hidden">
                                <img
                                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                                    alt="Profile"
                                    className="w-full h-full object-cover"
                                />
                            </div>
                        </div>

                        {/* Mobile menu button */}
                        <button
                            onClick={toggleMobileMenu}
                            className="lg:hidden inline-flex items-center justify-center p-1 sm:p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors duration-200"
                            aria-expanded="false"
                        >
                            <span className="sr-only">Open main menu</span>
                            {!isMobileMenuOpen ? (
                                <svg className="block h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            ) : (
                                <svg className="block h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile menu - Responsive for all small screens */}
            <div className={`lg:hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
                }`}>
                <div className="px-2 pt-2 pb-3 space-y-1 bg-gray-50 border-t border-gray-200">
                    {navItems.map((item) => (
                        <a
                            key={item}
                            href="#"
                            onClick={() => setIsMobileMenuOpen(false)}
                            className={`block px-3 py-2 text-sm sm:text-base font-medium transition-colors duration-200 rounded-md ${currentPage === item
                                ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                }`}
                        >
                            {item}
                        </a>
                    ))}
                </div>
            </div>
        </nav>
    );
};

export default Navbar;