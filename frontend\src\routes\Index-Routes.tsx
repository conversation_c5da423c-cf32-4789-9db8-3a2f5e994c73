import { createBrowserRouter } from "react-router-dom";
import AppLayout from "@/layout/AppLayout";
import Dashboard from "@/pages/Dashboard";
import Inventory from "@/pages/Inventory";
import Sales from "@/pages/Sales";
import Settings from "@/pages/Settings";
import NotFound from "@/pages/NotFound";
import ErrorBoundary from "@/components/ErrorBoundary";

export const router = createBrowserRouter([
    {
        path: "/",
        element: <AppLayout />,
        errorElement: <ErrorBoundary />,
        children: [
            {
                index: true,
                element: <Dashboard />,
            },
            {
                path: "inventory",
                element: <Inventory />,
            },
            {
                path: "sales",
                element: <Sales />,
            },
            {
                path: "customers",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Customers</h1><p>Customer management coming soon...</p></div>,
            },
            {
                path: "reports",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Reports</h1><p>Reports and analytics coming soon...</p></div>,
            },
            {
                path: "settings",
                element: <Settings />,
            },
            {
                path: "*",
                element: <NotFound />,
            }
        ]
    }
])