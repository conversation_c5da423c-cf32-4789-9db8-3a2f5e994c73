import { createBrowserRouter } from "react-router-dom";
import { lazy } from "react";
const AppLayout = lazy(() => import("@/layout/AppLayout"));
const Dashboard = lazy(() => import("@/pages/Dashboard"));
const Inventory = lazy(() => import("@/pages/Inventory"));
const Sales = lazy(() => import("@/pages/Sales"));
const Settings = lazy(() => import("@/pages/Settings"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const ErrorBoundary = lazy(() => import("@/components/ErrorBoundary"));

export const router = createBrowserRouter([
    {
        path: "/",
        element: <AppLayout />,
        errorElement: <ErrorBoundary />,
        children: [
            {
                index: true,
                element: <Dashboard />,
            },
            {
                path: "inventory",
                element: <Inventory />,
            },
            {
                path: "sales",
                    element: <Sales />,
            },
            {
                path: "customers",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Customers</h1><p>Customer management coming soon...</p></div>,
            },
            {
                path: "reports",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Reports</h1><p>Reports and analytics coming soon...</p></div>,
            },
            {
                path: "settings",
                element: <Settings />,
            },
            {
                path: "*",
                element: <NotFound />,
            }
        ]
    }
])