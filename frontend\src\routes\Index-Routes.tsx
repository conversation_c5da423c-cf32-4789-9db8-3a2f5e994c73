import { createBrowserRouter } from "react-router-dom";
import AppLayout from "@/layout/AppLayout";
import Dashboard from "@/pages/Dashboard";
import Inventory from "@/pages/Inventory";
import Sales from "@/pages/Sales";

export const router = createBrowserRouter([
    {
        path: "/",
        element: <AppLayout />,
        children: [
            {
                index: true,
                element: <Dashboard />,
            },
            {
                path: "dashboard",
                element: <Dashboard />,
            },
            {
                path: "inventory",
                element: <Inventory />,
            },
            {
                path: "sales",
                element: <Sales />,
            },
            {
                path: "customers",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Customers</h1><p>Customer management coming soon...</p></div>,
            },
            {
                path: "reports",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Reports</h1><p>Reports and analytics coming soon...</p></div>,
            },
            {
                path: "settings",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p>Application settings coming soon...</p></div>,
            }
        ]
    }
])